# نظام إدارة المفقس (Hatchery Management System)

هذا مشروع ويب بلغة بايثون باستخدام Flask وواجهة احترافية مع Bootstrap.

## الميزات الأساسية:
- إدارة دفعات البيض
- تتبع الفقس
- إدارة بيانات الفراخ
- تقارير الإنتاج
- تسجيل دخول المستخدمين

## المتطلبات
- Python 3.8+
- Flask
- Flask-Login
- Flask-SQLAlchemy
- Bootstrap (واجهة أمامية)

## خطوات التشغيل
1. تثبيت المتطلبات:
   ```powershell
   pip install -r requirements.txt
   ```
2. تشغيل التطبيق:
   ```powershell
   python app.py
   ```
3. افتح المتصفح على الرابط:
   http://127.0.0.1:5000

## ملاحظات
- يمكنك تخصيص النظام حسب احتياجات المفقس الخاص بك.
- جميع البيانات تحفظ في قاعدة بيانات SQLite افتراضيًا.
