{% extends 'base.html' %}
{% block content %}
<h2>تقارير الإنتاج</h2>
<table class="table table-striped">
    <thead>
        <tr>
            <th>اسم الدفعة</th>
            <th>تاريخ الإضافة</th>
            <th>عدد البيض</th>
            <th>عدد الفراخ المفقسة</th>
            <th>نسبة الفقس (%)</th>
        </tr>
    </thead>
    <tbody>
        {% for batch in batches %}
        <tr>
            <td>{{ batch.batch_name }}</td>
            <td>{{ batch.date_added }}</td>
            <td>{{ batch.eggs_count }}</td>
            <td>{{ batch.hatched_count }}</td>
            <td>{{ (batch.hatched_count / batch.eggs_count * 100) | round(2) if batch.eggs_count else 0 }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endblock %}
