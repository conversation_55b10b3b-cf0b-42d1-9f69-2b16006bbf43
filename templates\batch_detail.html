{% extends 'base.html' %}
{% block content %}
<h2>تفاصيل الدفعة: {{ batch.batch_name }}</h2>
<p>تاريخ الإضافة: {{ batch.date_added }}</p>
<p>عدد البيض: {{ batch.eggs_count }}</p>
<p>عدد الفراخ المفقسة: {{ batch.hatched_count }}</p>
<hr>
<h4>الفراخ المفقسة</h4>
<table class="table table-bordered">
    <thead>
        <tr>
            <th>تاريخ الفقس</th>
            <th>الحالة</th>
        </tr>
    </thead>
    <tbody>
        {% for chick in chicks %}
        <tr>
            <td>{{ chick.hatch_date }}</td>
            <td>{{ chick.status }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
<h5>إضافة فرخ جديد</h5>
<form method="post" action="/add_chick/{{ batch.id }}" class="w-50">
    <div class="mb-3">
        <label class="form-label">تاريخ الفقس</label>
        <input type="date" class="form-control" name="hatch_date" required>
    </div>
    <div class="mb-3">
        <label class="form-label">الحالة</label>
        <input type="text" class="form-control" name="status" required>
    </div>
    <button type="submit" class="btn btn-primary">إضافة</button>
</form>
{% endblock %}
