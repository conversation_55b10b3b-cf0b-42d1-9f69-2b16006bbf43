from flask import Flask, render_template, redirect, url_for, request, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>gin<PERSON><PERSON><PERSON>, login_user, login_required, logout_user, UserMixin, current_user
import os














































app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///hatchery.db'
db = SQLAlchemy(app)
login_manager = LoginManager(app)
login_manager.login_view = 'login'

# Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(150), unique=True, nullable=False)
    password = db.Column(db.String(150), nullable=False)

class EggBatch(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    batch_name = db.Column(db.String(100), nullable=False)
    date_added = db.Column(db.Date, nullable=False)
    eggs_count = db.Column(db.Integer, nullable=False)
    hatched_count = db.Column(db.Integer, default=0)

class Chick(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    batch_id = db.Column(db.Integer, db.ForeignKey('egg_batch.id'))
    hatch_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(50), nullable=False)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
@login_required
def index():
    batches = EggBatch.query.all()
    return render_template('index.html', batches=batches)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username, password=password).first()
        if user:
            login_user(user)
            return redirect(url_for('index'))
        else:
            flash('بيانات الدخول غير صحيحة')
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/add_batch', methods=['GET', 'POST'])
@login_required
def add_batch():
    if request.method == 'POST':
        batch_name = request.form['batch_name']
        date_added = request.form['date_added']
        eggs_count = request.form['eggs_count']
        batch = EggBatch(batch_name=batch_name, date_added=date_added, eggs_count=eggs_count)
        db.session.add(batch)
        db.session.commit()
        flash('تمت إضافة دفعة البيض بنجاح')
        return redirect(url_for('index'))
    return render_template('add_batch.html')

@app.route('/batch/<int:batch_id>')
@login_required
def batch_detail(batch_id):
    batch = EggBatch.query.get_or_404(batch_id)
    chicks = Chick.query.filter_by(batch_id=batch_id).all()
    return render_template('batch_detail.html', batch=batch, chicks=chicks)

@app.route('/add_chick/<int:batch_id>', methods=['POST'])
@login_required
def add_chick(batch_id):
    hatch_date = request.form['hatch_date']
    status = request.form['status']
    chick = Chick(batch_id=batch_id, hatch_date=hatch_date, status=status)
    db.session.add(chick)
    batch = EggBatch.query.get(batch_id)
    batch.hatched_count += 1
    db.session.commit()
    flash('تمت إضافة بيانات الفرخ')
    return redirect(url_for('batch_detail', batch_id=batch_id))

@app.route('/report')
@login_required
def report():
    batches = EggBatch.query.all()
    return render_template('report.html', batches=batches)

if __name__ == '__main__':
    with app.app_context():
        if not os.path.exists('hatchery.db'):
            db.create_all()
            # إنشاء مستخدم افتراضي للاختبار
            default_user = User(username='admin', password='admin123')
            db.session.add(default_user)
            db.session.commit()
            print("تم إنشاء قاعدة البيانات ومستخدم افتراضي (admin/admin123)")

    import threading
    import webbrowser
    import time
    import socket

    def check_port(port):
        """فحص إذا كان المنفذ متاح"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0

    def open_browser():
        """فتح المتصفح مع معالجة أفضل للأخطاء"""
        time.sleep(2)  # انتظار حتى يبدأ الخادم
        url = 'http://127.0.0.1:5000'

        for attempt in range(15):  # محاولة لمدة 15 ثانية
            try:
                if check_port(5000):  # فحص إذا كان الخادم يعمل
                    print(f"محاولة فتح المتصفح: {url}")
                    webbrowser.open(url)
                    print("تم فتح المتصفح بنجاح!")
                    break
                else:
                    print(f"الخادم لم يبدأ بعد... محاولة {attempt + 1}/15")
                    time.sleep(1)
            except Exception as e:
                print(f"خطأ في فتح المتصفح: {e}")
                time.sleep(1)
        else:
            print("فشل في فتح المتصفح تلقائياً. يرجى فتح الرابط يدوياً:")
            print("http://127.0.0.1:5000")

    # بدء thread لفتح المتصفح
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()

    print("بدء تشغيل خادم Flask...")
    print("الرابط: http://127.0.0.1:5000")
    print("للإيقاف: اضغط Ctrl+C")

    try:
        app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
    except Exception as e:
        print(f"خطأ في تشغيل الخادم: {e}")
        print("تأكد من أن المنفذ 5000 غير مستخدم من برنامج آخر")
