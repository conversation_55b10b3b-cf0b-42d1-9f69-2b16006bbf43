# Generated by Django 5.2.1 on 2025-05-26 14:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='EggBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_name', models.CharField(max_length=100)),
                ('date_added', models.DateField()),
                ('eggs_count', models.PositiveIntegerField()),
                ('hatched_count', models.PositiveIntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='Chick',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('hatch_date', models.DateField()),
                ('status', models.<PERSON><PERSON><PERSON><PERSON>(max_length=50)),
                ('batch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.eggbatch')),
            ],
        ),
    ]
