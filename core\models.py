from django.db import models
from django.contrib.auth.models import User

class EggBatch(models.Model):
    batch_name = models.CharField(max_length=100)
    date_added = models.DateField()
    eggs_count = models.PositiveIntegerField()
    hatched_count = models.PositiveIntegerField(default=0)

    def __str__(self):
        return self.batch_name

class Chick(models.Model):
    batch = models.ForeignKey(EggBatch, on_delete=models.CASCADE)
    hatch_date = models.DateField()
    status = models.CharField(max_length=50)

    def __str__(self):
        return f"{self.batch.batch_name} - {self.hatch_date}"
