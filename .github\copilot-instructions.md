<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

- هذا مشروع إدارة مفقس بلغة بايثون يعمل على الويب.
- يجب أن تكون الواجهة احترافية وسهلة الاستخدام.
- استخدم Flask مع Bootstrap للواجهة الأمامية.
- يجب أن يدعم النظام: إضافة دفعات بيض، تتبع الفقس، إدارة بيانات الفراخ، تقارير الإنتاج، وتسجيل الدخول للمستخدمين.
