<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المفقس</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
  <div class="container-fluid">
    <a class="navbar-brand" href="/">المفقس</a>
    <div class="collapse navbar-collapse">
      <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
        {% if current_user.is_authenticated %}
        <li class="nav-item"><a class="nav-link" href="/add_batch">إضافة دفعة</a></li>
        <li class="nav-item"><a class="nav-link" href="/report">تقارير الإنتاج</a></li>
        <li class="nav-item"><a class="nav-link" href="/logout">تسجيل الخروج</a></li>
        {% else %}
        <li class="nav-item"><a class="nav-link" href="/login">تسجيل الدخول</a></li>
        {% endif %}
      </ul>
    </div>
  </div>
</nav>
<div class="container">
    {% with messages = get_flashed_messages() %}
      {% if messages %}
        <div class="alert alert-info">{{ messages[0] }}</div>
      {% endif %}
    {% endwith %}
    {% block content %}{% endblock %}
</div>
</body>
</html>
